<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.CAMERA" />
<!--     <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />-->

    <application
        android:name="${applicationName}"
        android:icon="@mipmap/ic_launcher"
        android:label="UPSchool Teacher"
        android:requestLegacyExternalStorage="true">

<!--        <provider-->
<!--            android:name="vn.hunghd.flutterdownloader.DownloadedFileProvider"-->
<!--            android:authorities="${applicationId}.flutter_downloader.provider"-->
<!--            android:exported="false"-->
<!--            android:grantUriPermissions="true">-->
<!--            <meta-data-->
<!--                android:name="android.support.FILE_PROVIDER_PATHS"-->
<!--                android:resource="@xml/provider_paths" />-->
<!--        </provider>-->

        <provider
            android:name="vn.hunghd.flutterdownloader.DownloadedFileProvider"
            android:authorities="${applicationId}.flutter_downloader.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths" />
        </provider>


        <provider android:name="androidx.core.content.FileProvider"
            android:authorities="orgil.UpSchoolTeacher.TeacherApp.flutter_inappwebview_android.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/filepaths" />
        </provider>

        <activity
            android:name=".MainActivity"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:exported="true"
            android:hardwareAccelerated="true"
            android:launchMode="singleTop"
            android:theme="@style/LaunchTheme"
            android:usesCleartextTraffic="true"
            android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
                android:name="io.flutter.embedding.android.NormalTheme"
                android:resource="@style/NormalTheme" />
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
    </application>
</manifest>

<!--<manifest xmlns:android="http://schemas.android.com/apk/res/android">-->

<!--    <uses-permission android:name="android.permission.INTERNET" />-->
<!--    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />-->
<!--    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />-->
<!--&lt;!&ndash;    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />&ndash;&gt;-->
<!--    <uses-permission android:name="android.permission.CAMERA" />-->

<!--    <application-->
<!--        android:name="${applicationName}"-->
<!--        android:icon="@mipmap/ic_launcher"-->
<!--        android:label="UPSchool Teacher"-->
<!--        android:requestLegacyExternalStorage="true">-->

<!--&lt;!&ndash;        <provider&ndash;&gt;-->
<!--&lt;!&ndash;            android:name="vn.hunghd.flutterdownloader.DownloadedFileProvider"&ndash;&gt;-->
<!--&lt;!&ndash;            android:authorities="${applicationId}.flutter_downloader.provider"&ndash;&gt;-->
<!--&lt;!&ndash;            android:exported="false"&ndash;&gt;-->
<!--&lt;!&ndash;            android:grantUriPermissions="true">&ndash;&gt;-->
<!--&lt;!&ndash;            <meta-data&ndash;&gt;-->
<!--&lt;!&ndash;                android:name="android.support.FILE_PROVIDER_PATHS"&ndash;&gt;-->
<!--&lt;!&ndash;                android:resource="@xml/provider_paths" />&ndash;&gt;-->
<!--&lt;!&ndash;        </provider>&ndash;&gt;-->

<!--        <activity-->
<!--            android:name=".MainActivity"-->
<!--            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"-->
<!--            android:exported="true"-->
<!--            android:hardwareAccelerated="true"-->
<!--            android:launchMode="singleTop"-->
<!--            android:theme="@style/LaunchTheme"-->
<!--            android:usesCleartextTraffic="true"-->
<!--            android:windowSoftInputMode="adjustResize">-->
<!--            &lt;!&ndash; Specifies an Android theme to apply to this Activity as soon as-->
<!--                 the Android process has started. This theme is visible to the user-->
<!--                 while the Flutter UI initializes. After that, this theme continues-->
<!--                 to determine the Window background behind the Flutter UI. &ndash;&gt;-->
<!--            <meta-data-->
<!--                android:name="io.flutter.embedding.android.NormalTheme"-->
<!--                android:resource="@style/NormalTheme" />-->
<!--            <intent-filter>-->
<!--                <action android:name="android.intent.action.MAIN" />-->
<!--                <category android:name="android.intent.category.LAUNCHER" />-->
<!--            </intent-filter>-->
<!--        </activity>-->
<!--        &lt;!&ndash; Don't delete the meta-data below.-->
<!--             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java &ndash;&gt;-->
<!--        <meta-data-->
<!--            android:name="flutterEmbedding"-->
<!--            android:value="2" />-->

<!--        <provider-->
<!--            android:name="androidx.core.content.FileProvider"-->
<!--            android:authorities="orgil.UpSchoolTeacher.TeacherApp.flutter_inappwebview_android.fileprovider"-->
<!--            android:exported="false"-->
<!--            android:grantUriPermissions="true">-->
<!--            <meta-data-->
<!--                android:name="android.support.FILE_PROVIDER_PATHS"-->
<!--                android:resource="@xml/provider_paths" />-->

<!--        </provider>-->

<!--    </application>-->
<!--</manifest>-->
