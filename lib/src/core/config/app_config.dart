import 'dart:async';
import 'dart:developer';

import 'package:flutter/material.dart';
// import 'package:webview_flutter/webview_flutter.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:restart_app/restart_app.dart';
import 'package:up_school_parent/src/core/utils/app_constants.dart';
import 'package:up_school_parent/src/core/utils/one_signal_service.dart';
import 'package:xr_helper/xr_helper.dart';

class AppConfig extends ChangeNotifier {
  //? Loading
  bool isLoading = true;

  Future<void> requestCameraPermission() async {
    final status = await Permission.camera.request();
    if (status == PermissionStatus.granted) {
      // Permission granted.
    } else if (status == PermissionStatus.denied) {
      // Permission denied.
    } else if (status == PermissionStatus.permanentlyDenied) {
      // Permission permanently denied.
    }
  }

  set loading(bool value) {
    isLoading = value;
    notifyListeners();
  }

  //? Check Internet Connection
  bool hasInternet = true;

  //? Initialize App
  Future<void> init() async {
    try {
      loading = true;

      // await AppConfig.addFcmTokenToUrl();

      await checkInternetConnection();

      if (!hasInternet) {
        loading = false;
        return;
      }

      log('URL ${AppConstants.appUrl}');
    } on Exception catch (e) {
      log('Error $e');

      loading = false;
    }
  }

  static Future<void> addFcmTokenToUrl() async {
    try {
      final token = OneSignalNotificationService.getUserId();

      AppConstants.appUrl += '?token=$token';
    } on Exception catch (e) {
      log('Error $e');
    }
  }

  //? Check Internet Connection
  Future<void> checkInternetConnection() async {
    hasInternet = await InternetConnectionChecker().hasConnection;
    notifyListeners();
  }

  // InAppWebViewController? webViewController;

  late final InAppWebViewController webViewController;

  //? with flutter_inappwebview package
  Future<void> onWebViewCreated(InAppWebViewController controller) async {
    try {
      webViewController = controller;

      await addTokenToLogin(controller: controller);

      notifyListeners();
    } on TimeoutException catch (_) {
      log('Timeout occurred');

      loading = false;

      Restart.restartApp();
    } on Exception catch (e) {
      log('Error $e');

      loading = false;

      Restart.restartApp();
    }
  }

  Future<void> addTokenToLogin({
    required InAppWebViewController controller,
  }) async {
    final currentUrl = await controller.getUrl();

    final uri = Uri.parse(currentUrl.toString());
    final tokenInUrl = uri.queryParameters['token'];

    final shouldAddToken = (tokenInUrl == null || tokenInUrl.trim().isEmpty) &&
        currentUrl.toString().contains('login');

    if (shouldAddToken) {
      Log.w('SHOULD ADD TOKEN------');
      final token = OneSignalNotificationService.getUserId();
      if (token.isEmpty) {
        Future.delayed(const Duration(seconds: 5), () async {
          await addTokenToLogin(controller: controller);
        });
      } else {
        await controller.loadUrl(
          urlRequest: URLRequest(
            url: WebUri('${AppConstants.appUrl}?token=$token'),
            // url: WebUri('${currentUrl.toString()}?token=$token'),
          ),
        );
      }
    }
  }

//? Add Token To Login
// Future<void> addTokenToLogin({
//   required InAppWebViewController controller,
// }) async {
//   final currentUrl = await controller.getUrl();
//
//   final shouldAddToken = currentUrl.toString() == AppConstants.appUrl &&
//       !currentUrl.toString().contains('token=');
//
//   if (shouldAddToken) {
//     Log.w('SHOULD ADD TOKEN------');
//     final token = OneSignalNotificationService.getUserId();
//     await controller.loadUrl(
//       urlRequest: URLRequest(
//         url: WebUri('${currentUrl.toString()}?token=$token'),
//       ),
//     );
//   }
// }
}
